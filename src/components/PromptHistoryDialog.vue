<template>
  <div>
    <el-dialog
      title="Prompt Version History"
      :visible.sync="dialogVisible"
      width="60%"
      append-to-body
      @closed="onDialogClosed"
    >
      <div class="dialog-header-actions">
        <el-button
          v-if="selectedVersionIds.length > 0"
          size="small"
          @click="clearCompareSelection"
          icon="el-icon-close"
        >
          清空对比选择 ({{ selectedVersionIds.length }}/2)
        </el-button>
        <el-button
          v-if="canCompare"
          size="small"
          type="primary"
          @click="performDiff"
          icon="el-icon-files"
        >
          对比已选版本
        </el-button>
      </div>

      <el-timeline v-if="sortedPromptVersions.length > 0">
        <el-timeline-item
          v-for="version in sortedPromptVersions"
          :key="version.id"
          :timestamp="new Date(version.timestamp).toLocaleString()"
          placement="top"
        >
          <el-card
            :class="{ 'selected-for-diff': selectedVersionIds.includes(version.id) }"
          >
            <h4>
              {{ version.source === 'initial' ? '初始生成' : (version.source === 'refined' ? '优化版本' : '加载/编辑') }}
            </h4>
            <p class="prompt-preview">{{ version.content.substring(0, 150) }}...</p>
            <div class="card-actions">
              <el-button size="mini" @click="handleViewFullPromptVersion(version)">
                查看
              </el-button>
              <el-button size="mini" type="primary" @click="handleUsePromptVersion(version)">
                使用此版本
              </el-button>
              <el-button
                size="mini"
                :type="selectedVersionIds.includes(version.id) ? 'warning' : 'info'"
                @click="toggleSelectionForCompare(version.id)"
                :disabled="selectedVersionIds.length === 2 && !selectedVersionIds.includes(version.id)"
              >
                {{ selectedVersionIds.includes(version.id) ? '已选择' : '选择对比' }}
              </el-button>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
      <el-empty description="暂无 Prompt 历史记录。" v-else></el-empty>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- View Full Prompt Version Dialog (Internal to this component) -->
    <el-dialog
      title="完整 Prompt 版本"
      :visible.sync="viewFullPromptContentDialogVisible"
      width="70%"
      append-to-body
    >
      <div class="result-content-viewer" style="max-height: 60vh; overflow-y: auto;">
        <div v-if="currentViewingPromptContent" v-html="marked(currentViewingPromptContent)"></div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewFullPromptContentDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- Diff Viewer Dialog -->
    <el-dialog
      :title="`Prompt 差异对比： ${version1Timestamp} vs ${version2Timestamp}`"
      :visible.sync="diffDialogVisible"
      width="80%"
      top="5vh"
      append-to-body
      @closed="onDiffDialogClosed"
    >
      <div class="diff-viewer-content" style="max-height: 70vh; overflow-y: auto;">
        <pre v-html="diffHtmlContent" class="diff-pre"></pre>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="diffDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="clearCompareSelection">重新选择</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { marked } from 'marked';
import { diff_match_patch } from 'diff-match-patch'; // 导入 diff-match-patch

export default {
  name: 'PromptHistoryDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    versions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      viewFullPromptContentDialogVisible: false,
      currentViewingPromptContent: null,
      
      // Diffing related data
      selectedVersionIds: [], // Stores IDs of selected versions for comparison
      diffDialogVisible: false,
      diffHtmlContent: '',
      version1Timestamp: '', // Timestamp of the first selected version
      version2Timestamp: '', // Timestamp of the second selected version
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit('update:visible', value);
      },
    },
    sortedPromptVersions() {
      // Sort by timestamp in descending order (latest first)
      return [...this.versions].sort((a, b) => b.timestamp - a.timestamp);
    },
    // 获取选中的两个版本对象
    selectedVersions() {
      // Find the actual version objects based on their IDs
      const versions = this.selectedVersionIds
        .map(id => this.versions.find(v => v.id === id))
        .filter(Boolean); // Remove null/undefined if version not found

      // Sort by timestamp to ensure consistent "old" and "new" for diff (oldest first)
      return versions.sort((a, b) => a.timestamp - b.timestamp);
    },
    // 是否可以进行对比（已选择两个版本）
    canCompare() {
      return this.selectedVersionIds.length === 2;
    }
  },
  methods: {
    marked, // Make marked available for the internal viewer

    handleViewFullPromptVersion(version) {
      this.currentViewingPromptContent = version.content;
      this.viewFullPromptContentDialogVisible = true;
    },
    handleUsePromptVersion(version) {
      this.$emit('use-version', version.content);
      this.dialogVisible = false; // Close history dialog after selection
      this.clearCompareSelection(); // Clear diff selection when a version is used
    },
    onDialogClosed() {
        // Reset internal state if necessary when main dialog closes
        this.viewFullPromptContentDialogVisible = false;
        this.currentViewingPromptContent = null;
        this.clearCompareSelection(); // Clear diff selection
        this.$emit('closed');
    },
    onDiffDialogClosed() {
        // Optional: Reset diff content when diff dialog closes
        this.diffHtmlContent = '';
    },

    // --- Diffing Methods ---
    toggleSelectionForCompare(versionId) {
      const index = this.selectedVersionIds.indexOf(versionId);
      if (index > -1) {
        // If already selected, deselect it
        this.selectedVersionIds.splice(index, 1);
      } else {
        // If not selected
        if (this.selectedVersionIds.length < 2) {
          // If less than 2 selected, just add
          this.selectedVersionIds.push(versionId);
        } else {
          // If 2 already selected, replace the oldest one (first in the array)
          const oldestId = this.selectedVersions[0].id; // Get the ID of the oldest selected version
          const oldestIndex = this.selectedVersionIds.indexOf(oldestId);
          this.selectedVersionIds.splice(oldestIndex, 1); // Remove it
          this.selectedVersionIds.push(versionId); // Add new one
          this.$message.info('已替换最早的选择。');
        }
      }
    },
    clearCompareSelection() {
      this.selectedVersionIds = [];
      this.diffDialogVisible = false;
      this.diffHtmlContent = '';
      this.version1Timestamp = '';
      this.version2Timestamp = '';
      this.$message.info('已清空对比选择。');
    },
    performDiff() {
      if (!this.canCompare) {
        this.$message.warning('请选择两个 Prompt 版本进行对比。');
        return;
      }

      const [version1, version2] = this.selectedVersions; // These are already sorted by timestamp

      if (!version1 || !version2) {
        this.$message.error('无法找到选定的版本内容。');
        this.clearCompareSelection();
        return;
      }

      this.version1Timestamp = new Date(version1.timestamp).toLocaleString();
      this.version2Timestamp = new Date(version2.timestamp).toLocaleString();

      this.diffHtmlContent = this.generateUnifiedDiffHtml(version1.content, version2.content);
      this.diffDialogVisible = true;
    },

    // Helper to escape HTML characters
    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#39;'
        };
        return text.replace(/[&<>"']/g, (m) => map[m]);
    },

    // Custom diffPrettyHtml helper to allow custom class names for inline diffs
    // This function will be used internally by generateUnifiedDiffHtml
    diffPrettyHtmlCustom(diffs, removedClass, addedClass) {
        let html = [];
        for (let i = 0; i < diffs.length; i++) {
            const op = diffs[i][0]; // Operation (0: equal, 1: insert, -1: delete)
            const text = diffs[i][1]; // Text
            const escapedText = this.escapeHtml(text);

            switch (op) {
                case 1: // Insert
                    html.push('<span class="' + addedClass + '">' + escapedText + '</span>');
                    break;
                case -1: // Delete
                    html.push('<span class="' + removedClass + '">' + escapedText + '</span>');
                    break;
                case 0: // Equal
                    html.push(escapedText); // For equal, just push the text
                    break;
            }
        }
        return html.join('');
    },

    // Generates GitHub-style unified diff HTML
    generateUnifiedDiffHtml(oldText, newText) {
        const dmp = new diff_match_patch();

        // 1. Line-level diff: Convert texts to array of lines, then diff them
        // This is crucial for getting line-based changes first
        const lineMap = dmp.diff_linesToChars_(oldText, newText);
        const diff = dmp.diff_main(lineMap.chars1, lineMap.chars2);
        dmp.diff_charsToLines_(diff, lineMap.lineArray); // Reconvert characters to lines

        dmp.diff_cleanupSemantic(diff); // Clean up for better readability (merge common blocks)

        let html = '';
        let oldLineNum = 1;
        let newLineNum = 1;

        // Iterate through the line-level diffs
        for (let i = 0; i < diff.length; i++) {
            const op = diff[i][0]; // Operation: -1=delete, 0=equal, 1=insert
            const text = diff[i][1]; // Content of the line(s)

            // Split the segment by newline to handle multiple lines within one diff part
            // The split will create an empty string at the end if 'text' ends with a newline.
            const linesInSegment = text.split('\n');

            // Handle trailing newline if the segment ends with it to avoid processing an extra empty line
            const hasTrailingNewline = text.endsWith('\n');

            linesInSegment.forEach((lineContent, index) => {
                // If it's the last line in segment and it's empty due to a trailing newline, skip it
                if (index === linesInSegment.length - 1 && lineContent.length === 0 && hasTrailingNewline) {
                    return;
                }

                let currentOldLineNum = '';
                let currentNewLineNum = '';
                let prefixChar = ' ';
                let lineClass = '';
                let renderedContent = '';

                // Check if this is a "modified" line (a removed line immediately followed by an added line)
                // This logic needs to check the *next* diff segment.
                // It also assumes both the old and new parts of the modified line are single lines.
                const isModifiedLine = (
                    op === -1 && // Current segment is a deletion
                    i + 1 < diff.length && // There's a next segment
                    diff[i + 1][0] === 1 && // The next segment is an insertion
                    linesInSegment.length === 1 && // Current deleted segment is a single line
                    diff[i + 1][1].split('\n').length === 1 // Next inserted segment is also a single line
                );

                if (op === 0) { // Unchanged line
                    lineClass = 'diff-line-unchanged';
                    prefixChar = ' ';
                    currentOldLineNum = oldLineNum++;
                    currentNewLineNum = newLineNum++;
                    renderedContent = this.escapeHtml(lineContent);
                } else if (op === -1) { // Removed line
                    if (isModifiedLine) {
                        // This is the old part of a modified line.
                        // We will render both old and new parts in one go using inline diffing.
                        lineClass = 'diff-line-modified-old'; // Apply specific background for modified lines
                        prefixChar = '-';
                        currentOldLineNum = oldLineNum++;
                        // currentNewLineNum is not incremented here, it's for the combined output
                        
                        const nextLineContent = diff[i + 1][1]; // Get the content of the added line
                        const inlineDiffs = dmp.diff_main(lineContent, nextLineContent);
                        dmp.diff_cleanupSemantic(inlineDiffs); // Clean up inline diffs
                        
                        // Render inline diff of old vs new line content
                        renderedContent = this.diffPrettyHtmlCustom(inlineDiffs, 'diff-removed-inline', 'diff-added-inline');
                        
                        // Increment newLineNum here as this "modified" block consumes a new line number
                        newLineNum++;
                        
                        // Skip the next segment in the main diff loop as it's already processed
                        i++; 
                    } else {
                        // Standard removed line (no corresponding added line for inline diff)
                        lineClass = 'diff-line-removed';
                        prefixChar = '-';
                        currentOldLineNum = oldLineNum++;
                        // currentNewLineNum remains empty
                        renderedContent = `<span class="diff-removed-inline">${this.escapeHtml(lineContent)}</span>`;
                    }
                } else if (op === 1) { // Added line
                    // This 'op === 1' case is only for standalone added lines,
                    // as the 'new' part of a modified line is handled within the 'op === -1' block with `isModifiedLine`.
                    lineClass = 'diff-line-added';
                    prefixChar = '+';
                    // currentOldLineNum remains empty
                    currentNewLineNum = newLineNum++;
                    renderedContent = `<span class="diff-added-inline">${this.escapeHtml(lineContent)}</span>`;
                }

                html += `<div class="diff-line ${lineClass}">`;
                html += `<span class="diff-line-num old-num">${currentOldLineNum}</span>`;
                html += `<span class="diff-line-num new-num">${currentNewLineNum}</span>`;
                html += `<span class="diff-prefix">${prefixChar}</span>`;
                html += `<span class="diff-content">${renderedContent}</span>`;
                html += `</div>`;
            });
        }
        return html;
    }
  },
};
</script>

<style scoped>
/* Existing styles for prompt-preview, el-timeline, el-card, etc. */
.prompt-preview {
  font-size: 0.9em;
  color: #666;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.el-timeline-item__timestamp {
  font-size: 0.85em;
  color: #888;
}
.el-card h4 {
    margin-top: 0;
    margin-bottom: 10px;
}
.el-card {
    transition: all 0.2s ease-in-out;
}
.el-card.selected-for-diff {
    border: 2px solid #e6a23c; /* Orange border for selected items */
    box-shadow: 0 4px 15px rgba(230, 162, 60, 0.2);
    transform: translateY(-1px);
}
.card-actions {
    margin-top: 15px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}
/* Styles for the internal full prompt viewer */
.result-content-viewer {
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  border: 1px solid #e0e6ed;
  background-color: #fdfdfd;
}
.result-content-viewer :deep(h1),
.result-content-viewer :deep(h2),
.result-content-viewer :deep(h3) {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}
.result-content-viewer :deep(p) {
  margin-bottom: 1em;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  white-space: pre-wrap;
}
.result-content-viewer :deep(ul),
.result-content-viewer :deep(ol) {
  padding-left: 2em;
  margin-bottom: 1em;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}
.result-content-viewer :deep(pre) {
  background-color: #f1f1f1;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre;
}
.result-content-viewer :deep(code) {
  font-family: 'Courier New', monospace;
  background-color: #f1f1f1;
  padding: 2px 4px;
  border-radius: 3px;
}
.result-content-viewer :deep(pre code) {
  background-color: transparent;
  padding: 0;
}

/* Diff Viewer Specific Styles (New/Updated for GitHub-like diff) */
.diff-viewer-content {
  background-color: #f8f8f8; /* Light background for the diff area */
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden; /* Contains the scrollbar */
}

.diff-pre {
  margin: 0;
  padding: 0;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap; /* Allows text to wrap */
  word-break: break-word; /* Prevents long words from overflowing */
  overflow-x: auto; /* For horizontal scrolling if line is too long */
  min-height: 100px; /* Ensures minimum height */
  color: #333;
}

.diff-line {
  display: flex;
  align-items: flex-start; /* Align content to the top of the line */
  padding: 2px 0; /* Vertical padding for each line */
  border-bottom: 1px solid rgba(0,0,0,0.05); /* Subtle line separator */
}

.diff-line:last-child {
  border-bottom: none; /* No border for the last line */
}

.diff-line-num {
  flex-shrink: 0; /* Prevent line numbers from shrinking */
  width: 30px; /* Fixed width for line numbers */
  text-align: right;
  padding-right: 10px;
  color: #888;
  user-select: none; /* Prevent selection of line numbers */
}

.diff-prefix {
  flex-shrink: 0;
  width: 20px; /* Fixed width for +/-/space prefix */
  text-align: center;
  color: #888;
  user-select: none;
}

.diff-content {
  flex-grow: 1; /* Content takes remaining space */
  padding-left: 5px;
  white-space: pre-wrap; /* Ensure content wraps */
  word-break: break-word;
}

/* Line-level highlighting */
.diff-line-added {
  background-color: #e6ffed; /* Light green background for added lines */
}

.diff-line-removed {
  background-color: #ffeef0; /* Light red background for removed lines */
}

.diff-line-modified-old {
  /* This line class signifies a modified line where the content will show both removed and added inline */
  background-color: #fffbdd; /* Light yellow background for modified lines */
}

.diff-line-unchanged {
  background-color: transparent; /* No specific background for unchanged lines */
}

/* Inline highlighting */
.diff-added-inline {
  background-color: #b3f7d1; /* Brighter green for inline added text */
  color: #228b22; /* Dark green text */
  padding: 0 1px; /* Small padding to make highlight visible */
  border-radius: 2px;
}

.diff-removed-inline {
  background-color: #ffd8d8; /* Brighter red for inline removed text */
  color: #d12222; /* Dark red text */
  text-decoration: line-through; /* Strikethrough for removed text */
  padding: 0 1px;
  border-radius: 2px;
}

.dialog-header-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 20px;
}
</style>