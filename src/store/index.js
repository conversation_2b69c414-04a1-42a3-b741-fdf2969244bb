import Vue from 'vue'
import Vuex from 'vuex'
import { generatePrompt as apiGenerateCodePrompt, generatePrdPromptForAgent as apiGeneratePrdPrompt } from '@/lib/gemini'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    generatedCodePrompt: '',
    generatedPrdPrompt: '',
    isCodeGenerating: false,
    isPrdGenerating: false,
  },
  mutations: {
    SET_CODE_PROMPT(state, prompt) {
      if (typeof prompt === 'function') {
        state.generatedCodePrompt = prompt(state)
      } else {
        state.generatedCodePrompt = prompt
      }
    },
    APPEND_CODE_PROMPT(state, content) {
      state.generatedCodePrompt += content
    },
    SET_PRD_PROMPT(state, prompt) {
      if (typeof prompt === 'function') {
        state.generatedPrdPrompt = prompt(state)
      } else {
        state.generatedPrdPrompt = prompt
      }
    },
    APPEND_PRD_PROMPT(state, content) {
      state.generatedPrdPrompt += content
    },
    SET_CODE_GENERATING(state, status) {
      state.isCodeGenerating = status;
    },
    SET_PRD_GENERATING(state, status) {
      state.isPrdGenerating = status;
    },
  },
  actions: {
    async generateCodePrompt({ commit }, { image, appType, temperature, componentLibrary }) {
      commit('SET_CODE_GENERATING', true);
      commit('SET_CODE_PROMPT', '');

      try {
        const stream = await apiGenerateCodePrompt(image, appType, temperature, componentLibrary)
        if (!stream) {
          commit('SET_CODE_PROMPT', 'Error: No response from the model. Please try again.');
          return;
        }
        for await (const chunk of stream) {
          const content = chunk.choices[0]?.delta?.content || '';
          if (content) {
            commit('APPEND_CODE_PROMPT', content);
          }
        }
      } catch (error) {
        commit('SET_CODE_PROMPT', `Error generating code prompt: ${error.message || '未知错误'}. 请检查您的API配置或稍后重试。`);
      } finally {
        commit('SET_CODE_GENERATING', false);
      }
    },
    // generatePrdPrompt action 不再接收 stateManagement 和 router
    async generatePrdPrompt({ commit }, { featureDescription, framework, uiLibrary, temperature }) {
      commit('SET_PRD_GENERATING', true);
      commit('SET_PRD_PROMPT', '');

      try {
        // apiGeneratePrdPrompt 调用不再传递 stateManagement 和 router
        const stream = await apiGeneratePrdPrompt(featureDescription, framework, uiLibrary, temperature);
        if (!stream) {
          commit('SET_PRD_PROMPT', 'Error: No response from the model. Please try again.');
          return;
        }
        for await (const chunk of stream) {
          const content = chunk.choices[0]?.delta?.content || '';
          if (content) {
            commit('APPEND_PRD_PROMPT', content);
          }
        }
      } catch (error) {
        commit('SET_PRD_PROMPT', `Error generating PRD prompt: ${error.message || '未知错误'}. 请检查您的API配置或稍后重试。`);
      } finally {
        commit('SET_PRD_GENERATING', false);
      }
    }
  }
})